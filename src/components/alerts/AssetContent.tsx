import { Coin } from "@/lib/api.interface";
import USDThresholdChart from "./USDThresholdChart";
import { FILTER_LIST } from "../mofse-advance/constant";
// import { HeroSectionChart } from "../home/<USER>";
import type { ComponentType } from 'react';
import { CryptoTable } from "./CryptocurrenciesTable";
import { StableCoinTable } from "./StableCoinsTable";
import BitcoinGoldSpCharts from "./BitcoinGoldSpCharts";
import { TopGainersTable } from "./TopGainersTable";
import { TopLosersTable } from "./TopLosersTable";
// Economic Indicator Components
import M2vsBTCChart from "../economic-indicators/M2vsBTCChart";
import ConsumerIndexvsBTCChart from "../economic-indicators/ConsumerIndexvsBTCChart";
import TenYearTreasuryChart from "../economic-indicators/TenYearTreasuryChart";
import RetailSalesChart from "../economic-indicators/RetailSalesChart";
import FederalDebtChart from "../economic-indicators/FederalDebtChart";
import UnemploymentRateChart from "../economic-indicators/UnemploymentRateChart";
import FederalFundsRateChart from "../economic-indicators/FederalFundsRateChart";
// ETF & Funding Rates Components
import { BitcoinETFTable } from "./BitcoinETFTable";
import { EthereumETFTable } from "./EthereumETFTable";
import { FundingRatesTable } from "./FundingRatesTable";

interface AssetContentProps {
  coin: Coin;
  filter: string;
}

const chartComponentMap: Record<string, ComponentType<any>> = {
  'bitcoin-vs-gold-vs-sp500': BitcoinGoldSpCharts,
  'cryptocurrencies': CryptoTable,
  'stable-coins': StableCoinTable,
  'top-gainers': TopGainersTable,
  'top-losers': TopLosersTable,
  // Economic Indicators
  'm2-vs-btc': M2vsBTCChart,
  'consumer-index-vs-btc': ConsumerIndexvsBTCChart,
  'ten-year-treasury': TenYearTreasuryChart,
  'retail-sales': RetailSalesChart,
  'federal-debt': FederalDebtChart,
  'unemployment-rate': UnemploymentRateChart,
  'federal-funds-rate': FederalFundsRateChart,
  // ETF & Funding Rates Components
  'bitcoin-etf-data': BitcoinETFTable,
  'ethereum-etf-data': EthereumETFTable,
  'funding-rates-data': FundingRatesTable,
};

const AssetContent: React.FC<AssetContentProps> = ({ coin, filter }) => {

    function getHeading(filter: string) {
  switch (filter) {
    case FILTER_LIST.TRANSACTION_COUNT:
      return "Transaction Count";
    case FILTER_LIST.TRANSACTION_VOLUME:
      return "Transaction Volume";
    case FILTER_LIST.UNIQUE_SENDING_WALLET:
      return "Unique Sending Wallet";
    case FILTER_LIST.UNIQUE_RECEIVING_WALLET:
      return "Unique Receiving Wallet";
    case FILTER_LIST.TOTAL_UNIQUE_WALLET:
      return "Total Unique Wallet";
    case FILTER_LIST.NEW_WALLET_CREATED:
      return "New Wallet Created";
    case FILTER_LIST.BLOCK_MINED:
      return "Block Mined";
    case FILTER_LIST.AVG_TRANSACTION_VALUE:
      return "Average Transaction Value";
    case FILTER_LIST.TOTAL_FEE:
      return "Total Fee";
    case FILTER_LIST.WALLET_SENDING_GTE_1:
      return "Wallet Sending > 1 " + coin?.symbol;
    case FILTER_LIST.WALLET_SENDING_GTE_10:
      return "Wallet Sending > 10 " + coin?.symbol;
    case FILTER_LIST.WALLET_SENDING_GTE_100:
      return "Wallet Sending > 100 " + coin?.symbol;
    case FILTER_LIST.COIN_RANKINGS: 
      return "Coin Rankings";
    case FILTER_LIST.BTCVSGOLDVSSP500:
      return "BTC vs Gold vs S&P 500";
    case FILTER_LIST.TOP_GAINERS:
      return "Top Gainers";
    case FILTER_LIST.TOP_LOSERS:
      return "Top Losers";
    // Economic Indicators
    case FILTER_LIST.M2_VS_BTC:
      return "M2 vs BTC Price";
    case FILTER_LIST.CONSUMER_INDEX_VS_BTC:
      return "Consumer Index vs BTC";
    case FILTER_LIST.TEN_YEAR_TREASURY:
      return "10 Year Treasury vs BTC";
    case FILTER_LIST.RETAIL_SALES:
      return "Retail Sales vs BTC";
    case FILTER_LIST.FEDERAL_DEBT:
      return "Federal Debt vs BTC";
    case FILTER_LIST.UNEMPLOYMENT_RATE:
      return "Unemployment Rate vs BTC";
    case FILTER_LIST.FEDERAL_FUNDS_RATE:
      return "Federal Funds Rate vs BTC";
    // ETF & Funding Rates Data
    case FILTER_LIST.BITCOIN_ETF_DATA:
      return "Bitcoin ETF Data";
    case FILTER_LIST.ETHEREUM_ETF_DATA:
      return "Ethereum ETF Data";
    case FILTER_LIST.FUNDING_RATES_DATA:
      return "Funding Rates Data";
    default:
      return "";
  }
}

const ChartToRender = chartComponentMap[filter];

  return (
    <div className="flex">
      <div className="flex-1 py-6 pl-6 overflow-x-auto">
       {['cryptocurrencies'].includes(filter) ? null :
        <div className="flex items-center gap-4 mb-6">
          <h2 className="text-lg font-medium text-white">
            {['top-gainers', 'top-losers', 'bitcoin-vs-gold-vs-sp500', 'm2-vs-btc', 'consumer-index-vs-btc', 'ten-year-treasury', 'retail-sales', 'federal-debt', 'unemployment-rate', 'federal-funds-rate', 'bitcoin-etf-data', 'ethereum-etf-data', 'funding-rates-data'].includes(filter) ? getHeading(filter) : `${coin.symbol}: ${getHeading(filter)}`}
          </h2>
        </div>}
        <div className="space-y-6 ">
            {/* {filter === 'bitcoin-vs-gold-vs-sp500' ? <HeroSectionChart/>: 
          <USDThresholdChart coin={coin} onChainFilter={filter} />} */}
          {ChartToRender ? (
            <ChartToRender />
          ) : (
            <USDThresholdChart coin={coin} onChainFilter={filter} />
          )}
        </div>
      </div>
    </div>
  );
};

export default AssetContent;
