"use client";

import React, { useMemo } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { useFREDSeriesObservations } from '@/lib/state';
import { format, subYears } from 'date-fns';
import { formatChartDate } from '@/lib/utils';

import Loader from '../comman/Loader';
import FullscreenWrapper from '../comman/FullscreenWrapper';

const ConsumerIndexvsBTCChart: React.FC = () => {
  const endDate = format(new Date(), 'yyyy-MM-dd');
  const startDate = format(subYears(new Date(), 40), 'yyyy-MM-dd');

  // Fetch Consumer Price Index data
  const cpiQuery = useFREDSeriesObservations('CPIAUCNS', startDate, endDate);

  // Fetch Bitcoin price data from FRED (CBBTCUSD)
  const btcQuery = useFREDSeriesObservations('CBBTCUSD', startDate, endDate);

  // Process and merge data for dual-axis chart
  const chartData = useMemo(() => {
    if (!cpiQuery.data?.observations) return [];

    const cpiData = cpiQuery.data.observations
      .filter(obs => obs.value !== '.' && obs.value !== '' && obs.value !== null)
      .map(obs => ({
        date: obs.date,
        cpi: parseFloat(obs.value),
      }));

    // Create Bitcoin data map (may be empty if no data)
    const btcMap = new Map();
    if (btcQuery.data?.observations) {
      btcQuery.data.observations
        .filter(obs => obs.value !== '.' && obs.value !== '' && obs.value !== null)
        .forEach(obs => {
          btcMap.set(obs.date, parseFloat(obs.value));
        });
    }

    // Merge data by date - include all CPI data, Bitcoin where available
    const mergedData = cpiData
      .map(cpiItem => ({
        date: cpiItem.date,
        cpi: cpiItem.cpi,
        btc: btcMap.get(cpiItem.date) || null, // null if no Bitcoin data for this date
        formattedDate: formatChartDate(cpiItem.date, { showDay: true }),
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    return mergedData;
  }, [cpiQuery.data, btcQuery.data]);

  const formatCPIValue = (value: number) => {
    return value.toFixed(1);
  };

  const formatBTCValue = (value: number) => {
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(1)}K`;
    }
    return `$${value.toLocaleString()}`;
  };

  const isLoading = cpiQuery.isLoading;

  if (isLoading) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center" style={{ height: 500 }}>
          <Loader />
        </div>
      </div>
    );
  }

  if (!chartData || chartData.length === 0) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center flex-col space-y-4" style={{ height: 500 }}>
          <p className="text-white text-lg">Economic Data Coming Soon</p>
          <p className="text-gray-400 text-sm text-center max-w-md">
            The FRED API integration is ready. Backend endpoints are being deployed to fetch economic indicators data.
          </p>
        </div>
      </div>
    );
  }

  return (
    <FullscreenWrapper title="Consumer Price Index vs Bitcoin Price">
      <div className="w-full space-y-8 p-6">
      <div className="space-y-8">
        <div className="w-full space-y-4">
          <h2 className="text-2xl font-bold text-white">Consumer Price Index vs Bitcoin Price</h2>
          <div className="w-full">
            <div style={{ width: "100%", height: 500 }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={chartData}
                  margin={{ top: 20, right: 80, left: 20, bottom: 20 }}
                >
                  <CartesianGrid stroke="#5d5e5f" strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    tick={{ fill: "#fff", fontSize: 12 }}
                    tickFormatter={(value) => {
                      const date = new Date(value);
                      return date.getFullYear().toString();
                    }}
                    tickLine={false}
                    axisLine={false}
                    interval="preserveStartEnd"
                    minTickGap={40}
                  />
                  {/* Left Y-axis for CPI */}
                  <YAxis
                    yAxisId="cpi"
                    orientation="left"
                    tick={{ fill: "#4ECDC4", fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                    tickFormatter={formatCPIValue}
                    label={{
                      value: "CPI Index (1982-1984=100)",
                      angle: -90,
                      position: "insideLeft",
                      style: { fill: "#4ECDC4", fontSize: 14, fontWeight: 500 },
                    }}
                  />
                  {/* Right Y-axis for Bitcoin */}
                  <YAxis
                    yAxisId="btc"
                    orientation="right"
                    tick={{ fill: "#F7931A", fontSize: 12 }}
                    tickLine={false}
                    axisLine={false}
                    tickFormatter={formatBTCValue}
                    label={{
                      value: "Bitcoin Price (USD)",
                      angle: 90,
                      position: "insideRight",
                      offset: -5,
                      style: { fill: "#F7931A", fontSize: 14, fontWeight: 500 },
                    }}
                  />
                  <Tooltip
                    formatter={(val: number, name: string) => {
                      if (name === 'Consumer Price Index') {
                        return [formatCPIValue(val), name];
                      } else if (name === 'Bitcoin Price') {
                        return [formatBTCValue(val), name];
                      }
                      return [val, name];
                    }}
                    labelFormatter={(label) => `Date: ${formatChartDate(label, { showDay: true, showMonth: true, showYear: true })}`}
                    contentStyle={{
                      backgroundColor: "#1a1a1a",
                      border: "1px solid #333",
                      borderRadius: "8px",
                      color: "#ffffff",
                    }}
                  />
                  {/* CPI Line */}
                  <Line
                    yAxisId="cpi"
                    type="monotone"
                    dataKey="cpi"
                    stroke="#4ECDC4"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 4, fill: "#4ECDC4" }}
                    name="Consumer Price Index"
                  />
                  {/* Bitcoin Line */}
                  <Line
                    yAxisId="btc"
                    type="monotone"
                    dataKey="btc"
                    stroke="#F7931A"
                    strokeWidth={2}
                    dot={false}
                    activeDot={{ r: 4, fill: "#F7931A" }}
                    name="Bitcoin Price"
                    connectNulls={false}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>

        {/* Information about CPI vs BTC */}
        <div className="bg-gray-800 p-4 rounded-lg">
          <h3 className="text-white text-lg font-semibold mb-2">CPI vs Bitcoin Relationship</h3>
          <p className="text-gray-400 text-sm">
            The Consumer Price Index (CPI) measures inflation by tracking the cost of goods and services.
            Bitcoin is often viewed as a hedge against inflation. When CPI rises significantly, it indicates
            currency debasement, which historically has driven demand for Bitcoin as a store of value.
            The dual-axis view shows how both metrics have evolved together over time.
          </p>
        </div>
      </div>
    </div>
    </FullscreenWrapper>
  );
};

export default ConsumerIndexvsBTCChart;
